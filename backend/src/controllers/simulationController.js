const { query } = require('../config/database');
const triService = require('../services/triService');
const llmService = require('../services/llmService');

// Iniciar novo simulado
const startSimulation = async (req, res) => {
  try {
    const userId = req.user.id;

    // Verificar se há simulado em andamento
    const existingResult = await query(
      'SELECT id FROM simulations WHERE user_id = $1 AND status = $2',
      [userId, 'in_progress']
    );

    if (existingResult.rows.length > 0) {
      return res.status(400).json({
        error: 'Simulado em andamento',
        message: 'Você já tem um simulado em andamento. Complete-o antes de iniciar outro.',
        simulationId: existingResult.rows[0].id
      });
    }

    // Criar novo simulado no banco
    const simulationResult = await query(
      `INSERT INTO simulations (user_id, status, started_at) 
       VALUES ($1, $2, CURRENT_TIMESTAMP) 
       RETURNING id`,
      [userId, 'in_progress']
    );

    const simulationId = simulationResult.rows[0].id;

    // Inicializar TRI
    const triState = triService.initializeSimulation(userId);

    res.json({
      message: 'Simulado iniciado com sucesso',
      simulationId,
      triState: triService.getSimulationStats(triState),
      nextQuestionDifficulty: triService.getNextDifficulty(triState)
    });

  } catch (error) {
    console.error('Erro ao iniciar simulado:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao iniciar simulado'
    });
  }
};

// Obter próxima questão do simulado
const getNextQuestion = async (req, res) => {
  try {
    const { simulationId } = req.params;
    const userId = req.user.id;

    // Verificar se o simulado pertence ao usuário
    const simulationResult = await query(
      'SELECT * FROM simulations WHERE id = $1 AND user_id = $2',
      [simulationId, userId]
    );

    if (simulationResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Simulado não encontrado',
        message: 'O simulado especificado não existe ou não pertence a você'
      });
    }

    const simulation = simulationResult.rows[0];

    if (simulation.status !== 'in_progress') {
      return res.status(400).json({
        error: 'Simulado não está ativo',
        message: 'Este simulado já foi finalizado'
      });
    }

    // Buscar respostas anteriores para reconstruir estado TRI
    const responsesResult = await query(
      `SELECT question_number, difficulty_level, is_correct 
       FROM simulation_questions 
       WHERE simulation_id = $1 AND user_answer IS NOT NULL
       ORDER BY question_number`,
      [simulationId]
    );

    // Reconstruir estado TRI
    const triState = triService.initializeSimulation(userId);
    
    for (const response of responsesResult.rows) {
      triService.updateAbility(triState, response.difficulty_level, response.is_correct);
    }

    // Verificar se o simulado deve terminar
    if (triState.isComplete) {
      await query(
        `UPDATE simulations 
         SET status = $1, completed_at = CURRENT_TIMESTAMP, 
             final_score = $2, estimated_ability = $3, confidence_level = $4
         WHERE id = $5`,
        ['completed', triState.correctAnswers / triState.questionCount * 100, 
         triState.currentAbility, triState.confidence, simulationId]
      );

      return res.json({
        message: 'Simulado concluído',
        isComplete: true,
        triReport: triService.generateTRIReport(triState)
      });
    }

    // Determinar dificuldade da próxima questão
    const nextDifficulty = triService.getNextDifficulty(triState);
    const difficultyCategory = triService.getDifficultyCategory(nextDifficulty);

    // Buscar questões anteriores para evitar repetição
    const previousQuestionsResult = await query(
      'SELECT question_text FROM simulation_questions WHERE simulation_id = $1',
      [simulationId]
    );
    const previousQuestions = previousQuestionsResult.rows.map(row => row.question_text);

    // Gerar nova questão
    const questionData = await llmService.generateQuestion({
      difficulty: difficultyCategory,
      contentArea: 'Safe and Effective Care Environment', // Pode ser randomizado
      questionType: 'multiple_choice',
      usedQuestions: previousQuestions
    });

    // Salvar questão no banco (sem resposta ainda)
    const questionResult = await query(
      `INSERT INTO simulation_questions (
        simulation_id, question_number, question_text, question_type,
        options, correct_answer, difficulty_level, content_area, rationale
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING id`,
      [
        simulationId,
        triState.questionCount + 1,
        questionData.question_text,
        questionData.question_type,
        JSON.stringify(questionData.options),
        JSON.stringify(questionData.correct_answer),
        nextDifficulty,
        questionData.content_area,
        JSON.stringify(questionData.rationale)
      ]
    );

    res.json({
      message: 'Próxima questão gerada',
      questionId: questionResult.rows[0].id,
      questionNumber: triState.questionCount + 1,
      question: {
        id: questionResult.rows[0].id,
        text: questionData.question_text,
        type: questionData.question_type,
        options: questionData.options,
        contentArea: questionData.content_area
      },
      triState: triService.getSimulationStats(triState),
      progress: {
        current: triState.questionCount + 1,
        min: 75, // triService.minQuestions
        max: 265 // triService.maxQuestions
      }
    });

  } catch (error) {
    console.error('Erro ao obter próxima questão:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao gerar próxima questão'
    });
  }
};

// Submeter resposta para questão
const submitAnswer = async (req, res) => {
  try {
    const { simulationId, questionId } = req.params;
    const { answer, timeSpent = 0 } = req.body;
    const userId = req.user.id;

    // Validações
    if (!answer) {
      return res.status(400).json({
        error: 'Resposta obrigatória',
        message: 'A resposta é obrigatória'
      });
    }

    // Verificar se a questão existe e pertence ao simulado
    const questionResult = await query(
      `SELECT sq.*, s.user_id 
       FROM simulation_questions sq
       JOIN simulations s ON sq.simulation_id = s.id
       WHERE sq.id = $1 AND sq.simulation_id = $2`,
      [questionId, simulationId]
    );

    if (questionResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Questão não encontrada',
        message: 'A questão especificada não existe'
      });
    }

    const question = questionResult.rows[0];

    if (question.user_id !== userId) {
      return res.status(403).json({
        error: 'Acesso negado',
        message: 'Você não tem permissão para responder esta questão'
      });
    }

    if (question.user_answer !== null) {
      return res.status(400).json({
        error: 'Questão já respondida',
        message: 'Esta questão já foi respondida'
      });
    }

    // Verificar se a resposta está correta
    console.log('🔍 Debug correct_answer:', question.correct_answer, typeof question.correct_answer);
    
    let correctAnswer;
    try {
      // Tentar fazer parse como JSON
      correctAnswer = JSON.parse(question.correct_answer);
    } catch (error) {
      // Se falhar, tratar como string simples e converter para array
      console.log('⚠️ correct_answer não é JSON válido, tratando como string:', question.correct_answer);
      correctAnswer = [question.correct_answer];
    }
    
    // Garantir que correctAnswer seja sempre um array
    if (!Array.isArray(correctAnswer)) {
      correctAnswer = [correctAnswer];
    }
    const userAnswer = Array.isArray(answer) ? answer : [answer];
    const isCorrect = JSON.stringify(correctAnswer.sort()) === JSON.stringify(userAnswer.sort());

    // Atualizar questão com a resposta
    await query(
      `UPDATE simulation_questions 
       SET user_answer = $1, is_correct = $2, time_spent = $3, answered_at = CURRENT_TIMESTAMP
       WHERE id = $4`,
      [JSON.stringify(userAnswer), isCorrect, timeSpent, questionId]
    );

    // Buscar todas as respostas para reconstruir estado TRI
    const responsesResult = await query(
      `SELECT question_number, difficulty_level, is_correct 
       FROM simulation_questions 
       WHERE simulation_id = $1 AND user_answer IS NOT NULL
       ORDER BY question_number`,
      [simulationId]
    );

    // Reconstruir e atualizar estado TRI
    const triState = triService.initializeSimulation(userId);
    
    for (const response of responsesResult.rows) {
      triService.updateAbility(triState, response.difficulty_level, response.is_correct);
    }

    // Atualizar estatísticas do simulado
    await query(
      `UPDATE simulations 
       SET total_questions = $1, correct_answers = $2, estimated_ability = $3, confidence_level = $4
       WHERE id = $5`,
      [triState.questionCount, triState.correctAnswers, triState.currentAbility, triState.confidence, simulationId]
    );

    // Preparar resposta
    const response = {
      message: 'Resposta submetida com sucesso',
      isCorrect,
      correctAnswer,
      rationale: JSON.parse(question.rationale),
      triState: triService.getSimulationStats(triState),
      isSimulationComplete: triState.isComplete
    };

    // Se o simulado foi concluído, finalizar
    if (triState.isComplete) {
      await query(
        `UPDATE simulations 
         SET status = $1, completed_at = CURRENT_TIMESTAMP, final_score = $2
         WHERE id = $3`,
        ['completed', (triState.correctAnswers / triState.questionCount) * 100, simulationId]
      );

      response.triReport = triService.generateTRIReport(triState);
    }

    res.json(response);

  } catch (error) {
    console.error('Erro ao submeter resposta:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao processar resposta'
    });
  }
};

// Obter histórico de simulados do usuário
const getSimulationHistory = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await query(
      `SELECT 
        id, status, total_questions, correct_answers, final_score,
        estimated_ability, confidence_level, started_at, completed_at,
        CASE 
          WHEN status = 'completed' AND estimated_ability >= 0 THEN 'pass'
          WHEN status = 'completed' AND estimated_ability < 0 THEN 'fail'
          ELSE 'in_progress'
        END as predicted_result
       FROM simulations 
       WHERE user_id = $1 
       ORDER BY started_at DESC`,
      [userId]
    );

    const simulations = result.rows.map(sim => ({
      ...sim,
      accuracy: sim.total_questions > 0 ? 
        Math.round((sim.correct_answers / sim.total_questions) * 10000) / 100 : 0,
      duration: sim.completed_at ? 
        Math.round((new Date(sim.completed_at) - new Date(sim.started_at)) / 60000) : null
    }));

    res.json({
      simulations,
      summary: {
        total: simulations.length,
        completed: simulations.filter(s => s.status === 'completed').length,
        averageScore: simulations.length > 0 ? 
          simulations.reduce((sum, s) => sum + (s.final_score || 0), 0) / simulations.length : 0
      }
    });

  } catch (error) {
    console.error('Erro ao obter histórico:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao obter histórico de simulados'
    });
  }
};

// Obter detalhes de um simulado específico
const getSimulationDetails = async (req, res) => {
  try {
    const { simulationId } = req.params;
    const userId = req.user.id;

    // Buscar simulado
    const simulationResult = await query(
      'SELECT * FROM simulations WHERE id = $1 AND user_id = $2',
      [simulationId, userId]
    );

    if (simulationResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Simulado não encontrado',
        message: 'O simulado especificado não existe'
      });
    }

    const simulation = simulationResult.rows[0];

    // Buscar questões do simulado
    const questionsResult = await query(
      `SELECT 
        question_number, question_text, question_type, options, 
        correct_answer, user_answer, is_correct, difficulty_level,
        content_area, rationale, time_spent, answered_at
       FROM simulation_questions 
       WHERE simulation_id = $1 
       ORDER BY question_number`,
      [simulationId]
    );

    const questions = questionsResult.rows.map(q => ({
      ...q,
      options: JSON.parse(q.options),
      correct_answer: JSON.parse(q.correct_answer),
      user_answer: q.user_answer ? JSON.parse(q.user_answer) : null,
      rationale: JSON.parse(q.rationale)
    }));

    // Gerar relatório TRI se o simulado estiver completo
    let triReport = null;
    if (simulation.status === 'completed') {
      const triState = triService.initializeSimulation(userId);
      
      for (const question of questions.filter(q => q.user_answer !== null)) {
        triService.updateAbility(triState, question.difficulty_level, question.is_correct);
      }
      
      triReport = triService.generateTRIReport(triState);
    }

    res.json({
      simulation: {
        ...simulation,
        accuracy: simulation.total_questions > 0 ? 
          Math.round((simulation.correct_answers / simulation.total_questions) * 10000) / 100 : 0
      },
      questions,
      triReport
    });

  } catch (error) {
    console.error('Erro ao obter detalhes do simulado:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao obter detalhes do simulado'
    });
  }
};

module.exports = {
  startSimulation,
  getNextQuestion,
  submitAnswer,
  getSimulationHistory,
  getSimulationDetails
};

