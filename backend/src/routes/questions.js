const express = require('express');
const router = express.Router();
const questionController = require('../controllers/questionController');
const { authenticateToken, optionalAuth } = require('../middleware/auth');

// Rotas públicas (para demonstração)
router.get('/content-areas', (req, res) => {
  res.json({
    content_areas: questionController.CONTENT_AREAS,
    question_types: questionController.QUESTION_TYPES,
    difficulty_levels: questionController.DIFFICULTY_LEVELS
  });
});

// Gerar questão (pode ser usada sem autenticação para demonstração)
router.post('/generate', optionalAuth, questionController.generateQuestion);

// Rotas protegidas (requerem autenticação)
router.post('/save', authenticateToken, questionController.saveQuestion);
router.post('/translate', authenticateToken, questionController.translateQuestion);
router.get('/stats', authenticateToken, questionController.getQuestionStats);

// Rota para testar geração de questão (desenvolvimento)
router.get('/test-generate', async (req, res) => {
  try {
    // Simular uma requisição de geração
    const mockReq = {
      body: {
        difficulty: 'medium',
        contentArea: 'Safe and Effective Care Environment',
        questionType: 'multiple_choice'
      }
    };

    const mockRes = {
      json: (data) => res.json(data),
      status: (code) => ({
        json: (data) => res.status(code).json(data)
      })
    };

    await questionController.generateQuestion(mockReq, mockRes);
  } catch (error) {
    res.status(500).json({
      error: 'Erro no teste',
      message: error.message
    });
  }
});

module.exports = router;

