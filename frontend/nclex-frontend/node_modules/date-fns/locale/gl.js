import { formatDistance } from "./gl/_lib/formatDistance.js";
import { formatLong } from "./gl/_lib/formatLong.js";
import { formatRelative } from "./gl/_lib/formatRelative.js";
import { localize } from "./gl/_lib/localize.js";
import { match } from "./gl/_lib/match.js";

/**
 * @category Locales
 * @summary Galician locale.
 * @language Galician
 * @iso-639-2 glg
 * <AUTHOR>
 * <AUTHOR> Pita [@fidelpita](https://github.com/fidelpita)
 */
export const gl = {
  code: "gl",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default gl;
