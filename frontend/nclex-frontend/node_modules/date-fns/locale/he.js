import { formatDistance } from "./he/_lib/formatDistance.js";
import { formatLong } from "./he/_lib/formatLong.js";
import { formatRelative } from "./he/_lib/formatRelative.js";
import { localize } from "./he/_lib/localize.js";
import { match } from "./he/_lib/match.js";

/**
 * @category Locales
 * @summary Hebrew locale.
 * @language Hebrew
 * @iso-639-2 heb
 * <AUTHOR> [@nirlah](https://github.com/nirlah)
 */
export const he = {
  code: "he",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default he;
