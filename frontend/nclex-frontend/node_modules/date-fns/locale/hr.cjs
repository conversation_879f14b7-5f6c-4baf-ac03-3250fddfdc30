"use strict";
exports.hr = void 0;
var _index = require("./hr/_lib/formatDistance.cjs");
var _index2 = require("./hr/_lib/formatLong.cjs");
var _index3 = require("./hr/_lib/formatRelative.cjs");
var _index4 = require("./hr/_lib/localize.cjs");
var _index5 = require("./hr/_lib/match.cjs");

/**
 * @category Locales
 * @summary Croatian locale.
 * @language Croatian
 * @iso-639-2 hrv
 * <AUTHOR> [@silvenon](https://github.com/silvenon)
 * <AUTHOR> [@manico](https://github.com/manico)
 * <AUTHOR> [@jerzabek](https://github.com/jerzabek)
 */
const hr = (exports.hr = {
  code: "hr",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
