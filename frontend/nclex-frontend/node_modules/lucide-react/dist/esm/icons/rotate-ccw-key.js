/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m14.5 9.5 1 1", key: "159eiq" }],
  ["path", { d: "m15.5 8.5-4 4", key: "iirg3q" }],
  ["path", { d: "M3 12a9 9 0 1 0 9-9 9.74 9.74 0 0 0-6.74 2.74L3 8", key: "g2jlw" }],
  ["path", { d: "M3 3v5h5", key: "1xhq8a" }],
  ["circle", { cx: "10", cy: "14", r: "2", key: "1239so" }]
];
const RotateCcwKey = createLucideIcon("rotate-ccw-key", __iconNode);

export { __iconNode, RotateCcwKey as default };
//# sourceMappingURL=rotate-ccw-key.js.map
