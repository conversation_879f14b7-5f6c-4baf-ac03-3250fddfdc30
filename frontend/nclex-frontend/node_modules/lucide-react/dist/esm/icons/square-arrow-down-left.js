/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "m16 8-8 8", key: "166keh" }],
  ["path", { d: "M16 16H8V8", key: "1w2ppm" }]
];
const SquareArrowDownLeft = createLucideIcon("square-arrow-down-left", __iconNode);

export { __iconNode, SquareArrowDownLeft as default };
//# sourceMappingURL=square-arrow-down-left.js.map
