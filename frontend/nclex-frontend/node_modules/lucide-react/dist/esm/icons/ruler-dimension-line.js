/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 15v-3.014", key: "aw6ppf" }],
  ["path", { d: "M16 15v-3.014", key: "9e0vc7" }],
  ["path", { d: "M20 6H4", key: "1lfz86" }],
  ["path", { d: "M20 8V4", key: "1l2g47" }],
  ["path", { d: "M4 8V4", key: "sppxzt" }],
  ["path", { d: "M8 15v-3.014", key: "when08" }],
  ["rect", { x: "3", y: "12", width: "18", height: "7", rx: "1", key: "1ucwdz" }]
];
const RulerDimensionLine = createLucideIcon("ruler-dimension-line", __iconNode);

export { __iconNode, RulerDimensionLine as default };
//# sourceMappingURL=ruler-dimension-line.js.map
